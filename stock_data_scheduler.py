#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股数据定时采集脚本
开盘时间（9:30-15:00）每5分钟执行一次，收盘时间每30分钟执行一次
执行 dabanke_success.py 和 wencai_formatted.py 的main函数
"""

import datetime
import time
import logging
import sys
import os
import signal
from pathlib import Path

def check_dependencies():
    """检查依赖文件是否存在"""
    required_files = [
        "dabanke_success.py",
        "wencai_formatted.py"
    ]

    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)

    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保所有必要文件都在当前目录中。")
        return False

    print("✅ 所有必要文件检查通过")
    return True

def check_python_packages():
    """检查Python包依赖"""
    # 包名和导入名的映射
    package_imports = {
        "requests": "requests",
        "beautifulsoup4": "bs4",
        "pywencai": "pywencai",
        "pandas": "pandas"
    }

    missing_packages = []
    for package_name, import_name in package_imports.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print("❌ 缺少必要的Python包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    print("✅ 所有Python包依赖检查通过")
    return True

class StockDataScheduler:
    def __init__(self):
        self.last_execution_time = None
        self.running = True
        self.setup_logging()
        self.setup_signal_handlers()
        
    def setup_logging(self):
        """设置日志记录"""
        # 创建logs目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 按日期命名日志文件
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        log_file = log_dir / f"scheduler_{today}.log"
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_signal_handlers(self):
        """设置信号处理器，用于优雅退出"""
        def signal_handler(signum, frame):
            self.logger.info("收到退出信号，正在优雅退出...")
            self.running = False
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
    def is_weekday(self, now=None):
        """判断是否为工作日（周一到周五）"""
        if now is None:
            now = datetime.datetime.now()
        return now.weekday() < 5  # 0-4 表示周一到周五
        
    def is_trading_time(self, now=None):
        """判断是否为A股交易时间"""
        if now is None:
            now = datetime.datetime.now()
            
        # 首先检查是否为工作日
        if not self.is_weekday(now):
            return False
            
        current_time = now.time()
        
        # 上午交易时间：9:15-11:30
        morning_start = datetime.time(9, 15)
        morning_end = datetime.time(11, 30)
        
        # 下午交易时间：13:00-15:00
        afternoon_start = datetime.time(13, 0)
        afternoon_end = datetime.time(15, 0)
        
        return (morning_start <= current_time <= morning_end) or \
               (afternoon_start <= current_time <= afternoon_end)
               
    def should_execute(self, now=None):
        """判断是否应该执行脚本"""
        if now is None:
            now = datetime.datetime.now()
            
        # 如果从未执行过，立即执行
        if self.last_execution_time is None:
            return True
            
        # 计算距离上次执行的时间间隔
        time_diff = now - self.last_execution_time
        
        if self.is_trading_time(now):
            # 交易时间：每5分钟执行一次
            return time_diff.total_seconds() >= 300  # 5分钟 = 300秒
        else:
            # 非交易时间：每30分钟执行一次
            return time_diff.total_seconds() >= 300  # 30分钟 = 1800秒
            
    def execute_dabanke_script(self):
        """执行大板客脚本"""
        try:
            self.logger.info("开始执行 dabanke_success.py...")
            
            # 导入并执行dabanke_success模块的main函数
            import dabanke_success
            dabanke_success.main()
            
            self.logger.info("✅ dabanke_success.py 执行完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ dabanke_success.py 执行失败: {e}")
            return False
            
    def execute_wencai_script(self):
        """执行问财脚本"""
        try:
            self.logger.info("开始执行 wencai_formatted.py...")
            
            # 导入并执行wencai_formatted模块的main函数
            import wencai_formatted
            wencai_formatted.get_formatted_market_data()
            
            self.logger.info("✅ wencai_formatted.py 执行完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ wencai_formatted.py 执行失败: {e}")
            return False
            
    def execute_scripts(self):
        """执行两个脚本的main函数"""
        now = datetime.datetime.now()
        trading_status = "交易时间" if self.is_trading_time(now) else "非交易时间"
        
        self.logger.info(f"🚀 开始执行数据采集任务 - {trading_status}")
        self.logger.info(f"📅 执行时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行结果统计
        results = []
        
        # 执行大板客脚本
        dabanke_success = self.execute_dabanke_script()
        results.append(("dabanke_success.py", dabanke_success))
        
        # 执行问财脚本
        wencai_success = self.execute_wencai_script()
        results.append(("wencai_formatted.py", wencai_success))
        
        # 统计执行结果
        success_count = sum(1 for _, success in results if success)
        total_count = len(results)
        
        self.logger.info(f"📊 执行结果: {success_count}/{total_count} 个脚本成功执行")
        
        # 更新最后执行时间
        self.last_execution_time = now
        
        return success_count == total_count
        
    def get_next_execution_info(self):
        """获取下次执行时间信息"""
        now = datetime.datetime.now()
        
        if self.is_trading_time(now):
            # 交易时间，5分钟后执行
            next_time = now + datetime.timedelta(minutes=2)
            interval = "5分钟"
        else:
            # 非交易时间，30分钟后执行
            next_time = now + datetime.timedelta(minutes=30)
            interval = "30分钟"
            
        return next_time, interval
        
    def run(self):
        """主循环"""
        self.logger.info("🎯 A股数据定时采集脚本启动")
        self.logger.info("📋 执行规则:")
        self.logger.info("   - 交易时间(9:30-11:30, 13:00-15:00): 每5分钟执行一次")
        self.logger.info("   - 非交易时间: 每30分钟执行一次")
        self.logger.info("   - 仅在工作日的交易时间内按5分钟间隔执行")
        self.logger.info("="*60)
        
        # 启动时立即执行一次
        self.execute_scripts()
        
        while self.running:
            try:
                now = datetime.datetime.now()

                if self.should_execute(now):
                    self.execute_scripts()

                    # 只在执行完脚本后显示下次执行时间信息
                    next_time, interval = self.get_next_execution_info()
                    self.logger.info(f"⏰ 下次执行时间: {next_time.strftime('%H:%M:%S')} (间隔: {interval})")

                # 等待1分钟后再次检查
                time.sleep(60)
                
            except KeyboardInterrupt:
                self.logger.info("收到中断信号，正在退出...")
                break
            except Exception as e:
                self.logger.error(f"主循环发生错误: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续
                
        self.logger.info("📴 A股数据定时采集脚本已停止")

def main():
    """主函数"""
    print("🚀 A股数据定时采集脚本")
    print("="*50)

    # 检查文件依赖
    if not check_dependencies():
        sys.exit(1)

    # 检查包依赖
    if not check_python_packages():
        sys.exit(1)

    print("\n🎯 准备启动定时采集脚本...")
    print("📋 执行规则:")
    print("   - 交易时间(9:30-11:30, 13:00-15:00): 每5分钟执行一次")
    print("   - 非交易时间: 每30分钟执行一次")
    print("   - 按 Ctrl+C 可以优雅退出")
    print("\n" + "="*50)

    # 启动调度器
    try:
        scheduler = StockDataScheduler()
        scheduler.run()
    except KeyboardInterrupt:
        print("\n👋 用户主动退出")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
