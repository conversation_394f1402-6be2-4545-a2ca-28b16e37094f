#!/bin/bash

# 股票数据可视化系统部署脚本

echo "=================================="
echo "股票数据可视化系统部署脚本"
echo "=================================="

# 检查Python版本
echo "检查Python版本..."
python3 --version

# 检查pip
echo "检查pip..."
pip3 --version

# 安装依赖
echo "安装Python依赖..."
pip3 install -r requirements.txt

# 检查数据目录
echo "检查数据目录..."
if [ -d "../dabanke_data" ]; then
    echo "✓ 打板客数据目录存在"
    echo "  文件数量: $(ls -1 ../dabanke_data/*.json 2>/dev/null | wc -l)"
else
    echo "✗ 打板客数据目录不存在: ../dabanke_data"
fi

if [ -d "../market_data" ]; then
    echo "✓ 市场数据目录存在"
    echo "  文件数量: $(ls -1 ../market_data/*.json 2>/dev/null | wc -l)"
else
    echo "✗ 市场数据目录不存在: ../market_data"
fi

# 检查端口
echo "检查端口5000是否被占用..."
if lsof -Pi :5000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口5000已被占用，请先停止相关进程"
    echo "可以使用以下命令查看占用进程："
    echo "lsof -i :5000"
else
    echo "✓ 端口5000可用"
fi

# 创建启动脚本
echo "创建启动脚本..."
cat > start.sh << 'EOF'
#!/bin/bash
echo "启动股票数据可视化系统..."
python3 run.py
EOF

chmod +x start.sh

# 创建停止脚本
echo "创建停止脚本..."
cat > stop.sh << 'EOF'
#!/bin/bash
echo "停止股票数据可视化系统..."
pkill -f "python3 run.py"
pkill -f "python3 app.py"
echo "系统已停止"
EOF

chmod +x stop.sh

echo "=================================="
echo "部署完成！"
echo "=================================="
echo "启动命令: ./start.sh 或 python3 run.py"
echo "停止命令: ./stop.sh"
echo "访问地址: http://localhost:5000"
echo "=================================="
