<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票数据可视化</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>

<body>
    <div class="container-fluid">
        <!-- 页面标题和市场概览 -->
        <div class="row mb-2">
            <div class="col-lg-8">
                <h2 class="text-center text-primary mb-1">
                    <i class="fas fa-chart-line"></i> 数据分析
                </h2>
                <p class="text-center text-muted small">实时监控连板进度、市场概况和涨跌停统计</p>
            </div>
            <div class="col-lg-4">
                <!-- 市场概览 - 左右布局 -->
                <div class="card summary-card-horizontal">
                    <div class="card-body p-2">
                        <div class="d-flex align-items-center h-100">
                            <!-- 左侧：标题 -->
                            <div class="market-title">
                                <h6 class="mb-0"
                                    style="font-size: 0.75rem; writing-mode: vertical-lr; text-orientation: mixed;">
                                    <i class="fas fa-chart-bar"></i><br>大<br>盘
                                </h6>
                            </div>
                            <!-- 中间：横条区域 -->
                            <div class="market-bars-area flex-grow-1 mx-2">
                                <!-- 涨跌分布 -->
                                <div id="market-bars" class="market-bars-horizontal mb-1">
                                    <div class="text-center">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status"
                                            style="width: 1rem; height: 1rem;">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- 涨跌停分布 -->
                                <div id="limit-bars" class="limit-bars-horizontal mb-1">
                                    <div class="text-center">
                                        <div class="spinner-border spinner-border-sm text-warning" role="status"
                                            style="width: 1rem; height: 1rem;">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 右侧：成交金额 -->
                            <div class="volume-info-side">
                                <div id="volume-info" class="text-center">
                                    <div class="text-muted">
                                        <small style="font-size: 0.7rem;">加载中...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 左侧：连板金字塔 -->
            <div class="col-lg-4">
                <!-- 连板金字塔 -->
                <div class="card pyramid-card-new">
                    <div class="card-header py-2 text-center border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                        <h6 class="mb-0 font-semibold text-gray-800">连板金字塔</h6>
                        <small class="text-gray-500" id="pyramid-date">加载中...</small>
                    </div>
                    <div class="card-body p-3">
                        <!-- 金字塔布局 - 全宽度 -->
                        <div class="pyramid-layout-fullwidth">
                            <!-- 金字塔容器 - 占满整个宽度 -->
                            <div id="lianban-pyramid-visual" class="pyramid-container-fullwidth">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：趋势图表 -->
            <div class="col-lg-8">
                <div class="charts-container">

                    <!-- 连板胜率趋势 -->
                    <div class="card chart-card mb-2">
                        <div class="card-header py-1 d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">连板胜率趋势</h6>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm active" id="btn-main-levels"
                                    onclick="switchLianbanView('main')">主要级别</button>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="btn-high-levels"
                                    onclick="switchLianbanView('high')">高级别</button>
                            </div>
                        </div>
                        <div class="card-body py-2">
                            <div class="row">
                                <!-- 合并的图表 -->
                                <div class="col-md-12">
                                    <div class="chart-section">
                                        <div class="chart-container-merged">
                                            <canvas id="lianbanMergedChart"></canvas>
                                        </div>
                                        <span id="lianban-realtime-indicator" class="realtime-indicator">●</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 市场涨跌分布 -->
                    <div class="card chart-card mb-2">
                        <div class="card-header py-1">
                            <h6 class="mb-0">市场涨跌分布</h6>
                        </div>
                        <div class="card-body py-2">
                            <div class="row">
                                <!-- 合并的图表 -->
                                <div class="col-md-12">
                                    <div class="chart-section">
                                        <div class="chart-container-merged">
                                            <canvas id="marketMergedChart"></canvas>
                                        </div>
                                        <span id="market-realtime-indicator" class="realtime-indicator">●</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 涨跌停数量趋势 -->
                    <div class="card chart-card mb-2">
                        <div class="card-header py-1">
                            <h6 class="mb-0">涨跌停数量趋势</h6>
                        </div>
                        <div class="card-body py-2">
                            <div class="row">
                                <!-- 合并的图表 -->
                                <div class="col-md-12">
                                    <div class="chart-section">
                                        <div class="chart-container-merged">
                                            <canvas id="limitMergedChart"></canvas>
                                        </div>
                                        <span id="limit-realtime-indicator" class="realtime-indicator">●</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 成交金额趋势 -->
                    <div class="card chart-card mb-2">
                        <div class="card-header py-1">
                            <h6 class="mb-0">成交金额趋势</h6>
                        </div>
                        <div class="card-body py-2">
                            <div class="row">
                                <!-- 合并的图表 -->
                                <div class="col-md-12">
                                    <div class="chart-section">
                                        <div class="chart-container-merged">
                                            <canvas id="volumeMergedChart"></canvas>
                                        </div>
                                        <span id="volume-realtime-indicator" class="realtime-indicator">●</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 页脚 -->
        <footer class="row mt-2">
            <div class="col-12 text-center text-muted">
                <small>&copy; 2025 股票数据可视化系统 | 数据更新时间: <span id="last-update">加载中...</span></small>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="{{ url_for('static', filename='js/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
</body>

</html>