#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据可视化Web应用启动脚本
"""

import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from app import app

if __name__ == '__main__':

    print("=" * 50)
    print("股票数据可视化系统启动中...")
    print("=" * 50)
    print(f"访问地址: http://{app.config['HOST']}:{app.config['PORT']}")
    print(f"数据源: {app.config['DABANKE_DATA_PATH']} 和 {app.config['MARKET_DATA_PATH']}")
    print(f"调试模式: {'开启' if app.config['DEBUG'] else '关闭'}")
    print("=" * 50)

    # 检查数据目录是否存在
    dabanke_path = os.path.join(current_dir, app.config['DABANKE_DATA_PATH'])
    market_path = os.path.join(current_dir, app.config['MARKET_DATA_PATH'])

    if not os.path.exists(dabanke_path):
        print(f"警告: 打板客数据目录不存在: {dabanke_path}")
    else:
        dabanke_files = len([f for f in os.listdir(dabanke_path) if f.endswith('.json')])
        print(f"打板客数据文件数量: {dabanke_files}")

    if not os.path.exists(market_path):
        print(f"警告: 市场数据目录不存在: {market_path}")
    else:
        market_files = len([f for f in os.listdir(market_path) if f.endswith('.json')])
        print(f"市场数据文件数量: {market_files}")

    print("=" * 50)

    app.run(
        debug=app.config['DEBUG'],
        host=app.config['HOST'],
        port=app.config['PORT']
    )
