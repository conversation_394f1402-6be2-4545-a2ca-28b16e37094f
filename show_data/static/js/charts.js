// 股票数据可视化图表脚本

// 全局变量
let lianbanChart, marketChart, limitChart, volumeChart;
let lianbanRealtimeChart, marketRealtimeChart, limitRealtimeChart, volumeRealtimeChart;
let lianbanMergedChart, marketMergedChart, limitMergedChart, volumeMergedChart;
let currentLianbanData = null;
let currentMarketData = null;
let currentLimitData = null;
let currentVolumeData = null;
let currentLianbanView = 'main';
let realtimeDataCache = {
    lianban: [],
    market: [],
    limit: [],
    volume: []
};

// 颜色配置
const colors = {
    primary: '#007bff',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#343a40'
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    initializeCharts();
    loadAllData();
    loadRealtimeHistory(); // 加载当天实时数据历史

    // 设置定时刷新（历史数据每5分钟，实时数据每1分钟）
    setInterval(loadAllData, 5 * 60 * 1000);
    setInterval(loadRealtimeData, 60 * 1000);
});

// 初始化所有图表
function initializeCharts() {
    // 暂时注释掉原来的图表，只使用合并图表
    /*
    // 连板进度图表（如果存在）
    const lianbanElement = document.getElementById('lianbanChart');
    if (lianbanElement) {
        const lianbanCtx = lianbanElement.getContext('2d');
        lianbanChart = new Chart(lianbanCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        bottom: 25
                    }
                },
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                const dataIndex = context.dataIndex;
                                const datasetIndex = context.datasetIndex;
                                const level = context.dataset.label;
    
                                // 获取原始数据
                                if (currentLianbanData && currentLianbanData[dataIndex]) {
                                    const originalValue = currentLianbanData[dataIndex].progress[level]?.percentage || 0;
                                    const successCount = currentLianbanData[dataIndex].progress[level]?.success || 0;
                                    const totalCount = currentLianbanData[dataIndex].progress[level]?.total || 0;
    
                                    return `${level}: ${originalValue}% (${successCount}/${totalCount})`;
                                }
    
                                return `${level}: ${context.parsed.y}%`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        min: 0,
                        ticks: {
                            font: {
                                size: 10
                            },
                            stepSize: 20,
                            callback: function (value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 9
                            },
                            maxTicksLimit: 15, // 限制X轴标签数量
                            maxRotation: 0,
                            padding: 8
                        },
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    
        // 市场涨跌分布图表
        const marketCtx = document.getElementById('marketChart').getContext('2d');
        marketChart = new Chart(marketCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        bottom: 25
                    }
                },
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        min: 0,
                        max: 4000,
                        ticks: {
                            font: {
                                size: 10
                            },
                            stepSize: 500
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    
        // 涨跌停统计图表
        const limitCtx = document.getElementById('limitChart').getContext('2d');
        limitChart = new Chart(limitCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        min: 0,
                        max: 80,
                        ticks: {
                            font: {
                                size: 10
                            },
                            stepSize: 10
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    
        // 成交金额图表
        const volumeCtx = document.getElementById('volumeChart').getContext('2d');
        volumeChart = new Chart(volumeCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        min: 0,
                        max: 15000,
                        ticks: {
                            font: {
                                size: 10
                            },
                            stepSize: 2500,
                            callback: function (value) {
                                return formatAmount(value);
                            }
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
        */

    // 初始化实时图表
    // initializeRealtimeCharts();

    // 初始化合并图表
    initializeMergedCharts();
}

// 加载所有数据
async function loadAllData() {
    try {
        await Promise.all([
            loadLianbanData(),
            loadMarketData(),
            loadLimitData(),
            loadRealtimeData()  // 同时加载实时数据
        ]);

        updateLastUpdateTime();
    } catch (error) {
        console.error('加载数据失败:', error);
        showError('数据加载失败，请稍后重试');
    }
}

// 加载连板进度数据
async function loadLianbanData() {
    try {
        const response = await fetch('/api/lianban_progress');
        const result = await response.json();

        if (result.success) {
            updateLianbanChart(result.data);
            updateModernPyramid(result.data, result.latest_stocks);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('连板数据加载失败:', error);
    }
}

// 加载市场数据
async function loadMarketData() {
    try {
        const response = await fetch('/api/market_summary');
        const result = await response.json();

        if (result.success) {
            // 保存数据到全局变量
            currentMarketData = result.data;
            currentLimitData = result.data; // 涨跌停数据来自市场数据
            currentVolumeData = result.data; // 成交金额数据来自市场数据

            updateMarketChart(result.data);
            updateVolumeChart(result.data);
            updateMarketBars(result.data);
            // 市场数据中也包含涨跌停信息，直接更新
            updateLimitBars(result.data);

            // 更新合并图表
            updateMarketMergedChart();
            updateLimitMergedChart();
            updateVolumeMergedChart();
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('市场数据加载失败:', error);
    }
}

// 加载涨跌停数据
async function loadLimitData() {
    try {
        const response = await fetch('/api/limit_stats');
        const result = await response.json();

        if (result.success) {
            updateLimitChart(result.data);
            updateLimitBars(result.data);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('涨跌停数据加载失败:', error);
    }
}



// 更新市场图表
function updateMarketChart(data) {
    if (!data || data.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const dates = data.map(item => formatDate(item.date));

    marketChart.data.labels = dates;
    marketChart.data.datasets = [
        {
            label: '上涨家数',
            data: data.map(item => item.rise_count || item.up_count || 0),
            borderColor: colors.danger,  // 上涨用红色
            backgroundColor: colors.danger + '20',
            fill: false
        },
        {
            label: '下跌家数',
            data: data.map(item => item.fall_count || item.down_count || 0),
            borderColor: colors.success, // 下跌用绿色
            backgroundColor: colors.success + '20',
            fill: false
        }
    ];
    marketChart.update();
}

// 更新涨跌停图表
function updateLimitChart(data) {
    if (!data || data.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const dates = data.map(item => formatDate(item.date));

    limitChart.data.labels = dates;
    limitChart.data.datasets = [
        {
            label: '涨停数量',
            data: data.map(item => item.limit_up || item.limit_up_total || 0),
            backgroundColor: colors.danger,
            borderColor: colors.danger,
            borderWidth: 1
        },
        {
            label: '跌停数量',
            data: data.map(item => item.limit_down || item.limit_down_total || 0),
            backgroundColor: colors.success,
            borderColor: colors.success,
            borderWidth: 1
        }
    ];
    limitChart.update();
}

// 更新成交金额图表
function updateVolumeChart(data) {
    if (!data || data.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const dates = data.map(item => formatDate(item.date));

    volumeChart.data.labels = dates;
    volumeChart.data.datasets = [
        {
            label: '成交金额',
            data: data.map(item => item.volume || item.total_amount || 0),
            borderColor: colors.primary,
            backgroundColor: colors.primary + '20',
            fill: true,
            tension: 0.1
        }
    ];
    volumeChart.update();
}

// 工具函数
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return `${date.getMonth() + 1}/${date.getDate()}`;
}

function formatAmount(amount) {
    if (amount >= 1e12) {
        return (amount / 1e12).toFixed(1) + '万亿';
    } else if (amount >= 1e8) {
        return (amount / 1e8).toFixed(1) + '亿';
    } else if (amount >= 1e4) {
        return (amount / 1e4).toFixed(1) + '万';
    }
    return amount.toString();
}

function getColorByIndex(index, alpha = 1) {
    const colorList = [
        `rgba(255, 99, 132, ${alpha})`,
        `rgba(54, 162, 235, ${alpha})`,
        `rgba(255, 205, 86, ${alpha})`,
        `rgba(75, 192, 192, ${alpha})`,
        `rgba(153, 102, 255, ${alpha})`,
        `rgba(255, 159, 64, ${alpha})`
    ];
    return colorList[index % colorList.length];
}

function updateLastUpdateTime() {
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN');
    document.getElementById('last-update').textContent = timeStr;
}

function showError(message) {
    console.error(message);
    // 这里可以添加错误提示UI
}

// 更新连板概览
function updateLianbanSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];
    const progress = latest.progress;

    let html = '<div class="row">';
    Object.keys(progress).forEach(level => {
        const item = progress[level];
        html += `
        <div class="col-4 mb-1">
            <div class="summary-item">
                <div class="summary-value">${item.percentage}%</div>
                <div class="summary-label">${level}</div>
                <small class="text-muted" style="font-size: 0.65rem;">${item.success}/${item.total}</small>
            </div>
        </div>
    `;
    });
    html += '</div>';

    document.getElementById('lianban-summary').innerHTML = html;
}

// 更新市场概览
function updateMarketSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];

    // 计算总数和比例
    const totalStocks = latest.up_count + latest.down_count + (latest.flat_count || 0);

    // 更新涨跌分布 - 带对比横条
    const marketBars = document.getElementById('market-bars');
    if (marketBars) {
        const upPercent = totalStocks > 0 ? (latest.up_count / totalStocks * 100) : 0;
        const downPercent = totalStocks > 0 ? (latest.down_count / totalStocks * 100) : 0;
        const flatPercent = totalStocks > 0 ? ((latest.flat_count || 0) / totalStocks * 100) : 0;

        marketBars.innerHTML = `
        <div class="market-bar-row">
            <span class="bar-label rise">涨 ${latest.up_count}</span>
            <span class="bar-label fall">跌 ${latest.down_count}</span>
        </div>
        <div class="bar-container">
            <div class="bar-segment rise" style="width: ${upPercent}%"></div>
            <div class="bar-segment flat" style="width: ${flatPercent}%"></div>
            <div class="bar-segment fall" style="width: ${downPercent}%"></div>
        </div>
    `;
    }

    // 更新成交金额
    const volumeInfo = document.getElementById('volume-info');
    if (volumeInfo) {
        volumeInfo.innerHTML = `
        <div class="volume-amount">${latest.total_amount_formatted}</div>
        <div class="volume-label">成交金额</div>
    `;
    }
}

// 更新涨跌停概览
function updateLimitSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];

    // 更新涨跌停分布 - 带对比横条
    const limitBars = document.getElementById('limit-bars');
    if (limitBars) {
        const totalLimits = latest.limit_up_total + latest.limit_down_total;

        if (totalLimits > 0) {
            const limitUpPercent = (latest.limit_up_total / totalLimits * 100);
            const limitDownPercent = (latest.limit_down_total / totalLimits * 100);

            limitBars.innerHTML = `
            <div class="limit-bar-row">
                <span class="bar-label rise">涨停 ${latest.limit_up_total}</span>
                <span class="bar-label fall">跌停 ${latest.limit_down_total}</span>
            </div>
            <div class="bar-container">
                <div class="bar-segment limit-up" style="width: ${limitUpPercent}%"></div>
                <div class="bar-segment limit-down" style="width: ${limitDownPercent}%"></div>
            </div>
        `;
        } else {
            limitBars.innerHTML = `
            <div class="limit-bar-row">
                <span class="bar-label rise">涨停 ${latest.limit_up_total}</span>
                <span class="bar-label fall">跌停 ${latest.limit_down_total}</span>
            </div>
            <div class="bar-container">
                <div class="bar-segment limit-up" style="width: 50%"></div>
                <div class="bar-segment limit-down" style="width: 50%"></div>
            </div>
        `;
        }
    }
}

// 更新连板进度图表
function updateLianbanChart(data) {
    if (!data || data.length === 0) return;

    currentLianbanData = data; // 保存数据供切换使用
    // renderLianbanChart(currentLianbanView); // 暂时禁用

    // 更新合并图表
    updateLianbanMergedChart();
}

// 渲染连板图表
function renderLianbanChart(viewType) {
    if (!currentLianbanData || currentLianbanData.length === 0) return;

    const dates = currentLianbanData.map(item => formatDate(item.date));
    let levels, getColorFunc;

    if (viewType === 'main') {
        // 主要级别：首板到3进4
        levels = ['首板', '1进2', '2进3', '3进4'];
        getColorFunc = getMainLevelColor;
    } else {
        // 高级别：3进4到9进10
        levels = ['3进4', '5进6', '9进10'];
        getColorFunc = getHighLevelColor;
    }

    const datasets = levels.map((level, index) => {
        const rawData = currentLianbanData.map(item => item.progress[level]?.percentage || 0);

        // 对100%的数据点进行处理，避免图表失真
        const processedData = rawData.map(value => {
            if (value === 100) {
                return 95; // 将100%显示为95%，保持图表比例
            }
            return value;
        });

        return {
            label: level,
            data: processedData,
            borderColor: getColorFunc(index),
            backgroundColor: getColorFunc(index, 0.1),
            fill: false,
            tension: 0.3, // 增加平滑度
            borderWidth: 2,
            pointRadius: 2,
            pointHoverRadius: 4
        };
    });

    lianbanChart.data.labels = dates;
    lianbanChart.data.datasets = datasets;
    lianbanChart.update();
}

// 为主要级别定义更清晰的颜色
function getMainLevelColor(index, alpha = 1) {
    const colors = [
        `rgba(220, 53, 69, ${alpha})`,   // 首板 - 红色
        `rgba(40, 167, 69, ${alpha})`,   // 1进2 - 绿色
        `rgba(0, 123, 255, ${alpha})`,   // 2进3 - 蓝色
        `rgba(255, 193, 7, ${alpha})`    // 3进4 - 黄色
    ];
    return colors[index % colors.length];
}

// 为高级别定义颜色
function getHighLevelColor(index, alpha = 1) {
    const colors = [
        `rgba(255, 193, 7, ${alpha})`,   // 3进4 - 黄色
        `rgba(111, 66, 193, ${alpha})`,  // 5进6 - 紫色
        `rgba(255, 99, 132, ${alpha})`   // 9进10 - 粉色
    ];
    return colors[index % colors.length];
}

// 切换连板视图
function switchLianbanView(viewType) {
    currentLianbanView = viewType;

    // 更新按钮状态
    document.getElementById('btn-main-levels').classList.toggle('active', viewType === 'main');
    document.getElementById('btn-high-levels').classList.toggle('active', viewType === 'high');

    // 重新渲染图表
    renderLianbanChart(viewType);
}

// 更新现代金字塔
function updateModernPyramid(progressData, stocksData) {
    if (!progressData || progressData.length === 0) {
        document.getElementById('lianban-pyramid-visual').innerHTML = '<p class="text-muted text-center">暂无连板数据</p>';
        return;
    }

    // 获取最新一天的进度数据
    const latestProgress = progressData[progressData.length - 1].progress;

    // 定义所有可能的级别顺序（从高到低）
    const allLevels = ['9进10', '8进9', '7进8', '6进7', '5进6', '4进5', '3进4', '2进3', '1进2', '首板'];

    // 筛选出实际存在数据的级别
    const existingLevels = allLevels.filter(level => {
        const progressInfo = latestProgress[level];
        const stocks = stocksData ? (stocksData[level] || []) : [];
        return progressInfo && (progressInfo.total > 0 || stocks.length > 0);
    });

    // 如果没有任何级别有数据，显示提示信息
    if (existingLevels.length === 0) {
        document.getElementById('lianban-pyramid-visual').innerHTML = '<p class="text-muted text-center">暂无连板数据</p>';
        return;
    }

    // 更新成功率显示
    updateSuccessRatesDisplay(latestProgress, existingLevels);

    // 动态调整金字塔高度分配
    const containerElement = document.getElementById('lianban-pyramid-visual').parentElement;
    const totalHeight = containerElement.clientHeight - 40; // 减去padding
    const levelCount = existingLevels.length;

    // 计算总股票数量
    const totalStocks = existingLevels.reduce((total, level) => {
        const stocks = stocksData ? (stocksData[level] || []) : [];
        return total + stocks.length;
    }, 0);

    let html = '<div class="lianban-text-display">';

    existingLevels.forEach((level, index) => {
        const progressInfo = latestProgress[level];
        const stocks = stocksData ? (stocksData[level] || []) : [];

        const successRate = progressInfo.percentage;
        const stockCount = stocks.length;
        const successCount = progressInfo.success;
        const totalCount = progressInfo.total;

        // 计算该级别需要的最小高度（基于股票数量）
        const stocksPerRow = 4;
        const rowHeight = 20; // 调整为更紧凑的行高
        const requiredRows = Math.ceil(stockCount / stocksPerRow);
        const baseHeight = 50; // 每个级别的基础高度

        // 根据股票数量设置合理的最大高度限制
        let maxHeightForLevel;
        if (stockCount <= 2) {
            maxHeightForLevel = 80;  // 1-2只股票最多80px
        } else if (stockCount <= 5) {
            maxHeightForLevel = 100; // 3-5只股票最多100px
        } else if (stockCount <= 10) {
            maxHeightForLevel = 150; // 6-10只股票最多150px
        } else if (stockCount <= 20) {
            maxHeightForLevel = Math.floor(totalHeight * 0.4); // 中等级别最多40%
        } else {
            maxHeightForLevel = Math.floor(totalHeight * 0.6); // 大级别最多60%
        }

        // 按股票数量比例分配空间
        const heightRatio = stockCount / totalStocks;
        const proportionalHeight = Math.floor(totalHeight * heightRatio);

        // 确保高度在合理范围内：不少于基础高度，不超过该级别的最大限制
        const calculatedHeight = Math.max(baseHeight, Math.min(maxHeightForLevel, proportionalHeight));

        // 生成股票显示 - 每行4个
        let stocksHtml = '';
        let allStocksTooltip = '';

        if (stocks.length > 0) {
            // 按状态排序，成功的在前面
            const sortedStocks = stocks.sort((a, b) => {
                if (a.status === '成' && b.status !== '成') return -1;
                if (a.status !== '成' && b.status === '成') return 1;
                return parseFloat(b.change_percent) - parseFloat(a.change_percent);
            });

            // 动态计算显示股票数量
            const maxRows = Math.floor(calculatedHeight / 20); // 每行约20px高度
            const maxDisplayStocks = maxRows * 4;

            // 确保至少显示所有股票（如果数量不多的话）
            let finalMaxStocks = maxDisplayStocks;
            if (sortedStocks.length <= 10) {
                // 如果股票数量少于等于10只，全部显示
                finalMaxStocks = sortedStocks.length;
            } else if (sortedStocks.length <= 20 && maxDisplayStocks < sortedStocks.length) {
                // 如果股票数量在10-20之间，且计算的显示数量不足，适当增加
                finalMaxStocks = Math.min(sortedStocks.length, maxDisplayStocks + 8);
            }

            const displayStocks = sortedStocks.slice(0, finalMaxStocks);

            stocksHtml = displayStocks.map(stock => {
                const isSuccess = stock.status === '成';
                const isFailed = stock.status === '炸';
                const changePercent = parseFloat(stock.change_percent);
                let colorClass = 'text-success'; // 默认失败绿色
                if (isSuccess) {
                    colorClass = 'text-danger'; // 成功红色
                } else if (isFailed) {
                    colorClass = 'text-warning'; // 炸板黄色
                }
                const sign = changePercent >= 0 ? '+' : '';
                const statusText = isSuccess ? '成功' : (isFailed ? '炸板' : '失败');
                return `<span class="stock-item ${colorClass}" title="${stock.name} ${sign}${changePercent.toFixed(2)}% (${statusText})" onclick="showStockDetail('${stock.name}', '${stock.code}', '${stock.change_percent}', '${stock.industry || ''}', '${stock.status}')">${stock.name} ${sign}${changePercent.toFixed(2)}%</span>`;
            }).join('');

            // 生成所有股票的提示信息
            allStocksTooltip = sortedStocks.map(stock => {
                const changePercent = parseFloat(stock.change_percent);
                const sign = changePercent >= 0 ? '+' : '';
                const isSuccess = stock.status === '成';
                const isFailed = stock.status === '炸';
                const statusText = isSuccess ? '成功' : (isFailed ? '炸板' : '失败');
                return `${stock.name} ${sign}${changePercent.toFixed(2)}% (${statusText})`;
            }).join('\\n');
        }

        html += `
        <div class="lianban-level-row" style="min-height: ${calculatedHeight}px;" onclick="showLevelDetail('${level}', ${successRate}, ${successCount}, ${totalCount}, ${stockCount})">
            <div class="level-info">
                <span class="level-badge level-${level.replace('进', '-')}">${level}</span>
                <span class="level-stats" title="${allStocksTooltip}">${successCount}/${totalCount}</span>
            </div>
            <div class="stocks-list" style="max-height: none; min-height: ${Math.max(40, Math.ceil(stockCount / 4) * 20)}px;">
                ${stocksHtml}
            </div>
        </div>
    `;
    });

    html += '</div>';

    document.getElementById('lianban-pyramid-visual').innerHTML = html;

    // 更新日期显示
    const today = new Date().toLocaleDateString('zh-CN');
    document.getElementById('pyramid-date').textContent = today;
}

// 更新成功率显示
function updateSuccessRatesDisplay(latestProgress, levelOrder) {
    levelOrder.forEach(level => {
        const progressInfo = latestProgress[level];
        if (!progressInfo) return;

        const successRate = progressInfo.percentage || 0;

        const rateElement = document.querySelector(`.success-rate-item[data-level="${level}"] .rate-value`);
        if (rateElement) {
            rateElement.textContent = successRate > 0 ? `${successRate}%` : '-';
        }
    });
}

// 原有的金字塔函数保持兼容
function updateLianbanPyramidVisual(progressData, stocksData) {
    if (!progressData || progressData.length === 0) {
        document.getElementById('lianban-pyramid-visual').innerHTML = '<p class="text-muted text-center">暂无连板数据</p>';
        return;
    }

    // 获取最新一天的进度数据
    const latestProgress = progressData[progressData.length - 1].progress;

    // 定义所有可能的级别顺序（从高到低）
    const allLevels = ['9进10', '8进9', '7进8', '6进7', '5进6', '4进5', '3进4', '2进3', '1进2', '首板'];

    // 筛选出实际存在数据的级别
    const levelOrder = allLevels.filter(level => {
        const progressInfo = latestProgress[level];
        const stocks = stocksData ? (stocksData[level] || []) : [];
        return progressInfo && (progressInfo.total > 0 || stocks.length > 0);
    });

    // 如果没有任何级别有数据，显示提示信息
    if (levelOrder.length === 0) {
        document.getElementById('lianban-pyramid-visual').innerHTML = '<p class="text-muted text-center">暂无连板数据</p>';
        return;
    }

    let html = '<div class="pyramid-content">';

    levelOrder.forEach((level, index) => {
        const progressInfo = latestProgress[level];
        const stocks = stocksData ? (stocksData[level] || []) : [];

        if (!progressInfo) return;

        const successRate = progressInfo.percentage;
        const stockCount = stocks.length;
        const successCount = progressInfo.success;
        const totalCount = progressInfo.total;

        // 交替显示成功率在左右两侧
        const ratePosition = index % 2 === 0 ? 'success-rate-left' : 'success-rate-right';

        // 生成股票显示
        let stocksHtml = '';
        if (stocks.length > 0) {
            // 按状态排序，成功的在前面
            const sortedStocks = stocks.sort((a, b) => {
                if (a.status === '成' && b.status !== '成') return -1;
                if (a.status !== '成' && b.status === '成') return 1;
                return parseFloat(b.change_percent) - parseFloat(a.change_percent);
            });

            // 动态调整显示股票数量，确保所有级别的股票都能显示
            let maxStocks;
            if (level === '首板') {
                maxStocks = 35;
            } else if (level === '1进2') {
                maxStocks = 30;
            } else if (level === '2进3') {
                maxStocks = 25;
            } else if (level === '3进4') {
                maxStocks = 20;
            } else if (level === '4进5') {
                maxStocks = 15;
            } else if (level === '5进6') {
                maxStocks = 15;
            } else if (level === '6进7' || level === '7进8' || level === '8进9' || level === '9进10') {
                maxStocks = 12;
            } else {
                // 对于更高级别，显示所有股票
                maxStocks = sortedStocks.length;
            }

            // 如果实际股票数量少于限制，显示所有股票
            const displayStocks = sortedStocks.slice(0, Math.min(maxStocks, sortedStocks.length));

            stocksHtml = displayStocks.map(stock => {
                const isSuccess = stock.status === '成';
                const stockClass = isSuccess ? 'success' : 'fail';
                return `<span class="stock-mini ${stockClass}" title="${stock.name} ${stock.change_percent}%">${stock.name}</span>`;
            }).join('');
        }

        html += `
        <div class="pyramid-level pyramid-level-${level}"
             onclick="showLevelDetail('${level}', ${successRate}, ${successCount}, ${totalCount}, ${stockCount})"
             title="点击查看${level}详情">
            <div class="success-rate ${ratePosition}">
                ${successRate}%
                <br><small>${successCount}/${totalCount}</small>
            </div>
            <div class="level-title">${level}</div>
            <div class="level-stocks">
                ${stocksHtml}
            </div>
        </div>
    `;
    });

    html += '</div>';

    document.getElementById('lianban-pyramid-visual').innerHTML = html;

    // 更新日期显示
    const today = new Date().toLocaleDateString('zh-CN');
    document.getElementById('pyramid-date').textContent = today;
}

// 显示级别详情
function showLevelDetail(level, successRate, successCount, totalCount, stockCount) {
    const message = `${level}连板详情：

成功率: ${successRate}%
成功数量: ${successCount}只
尝试数量: ${totalCount}只
当前存在: ${stockCount}只股票

${successRate >= 50 ? '✅ 成功率较高，市场情绪良好' : '⚠️ 成功率偏低，需谨慎操作'}`;

    if (typeof showToast !== 'undefined') {
        const toastType = successRate >= 50 ? 'success' : 'warning';
        showToast(`${level}: ${successRate}% (${successCount}/${totalCount})`, toastType);
    } else {
        alert(message);
    }
}

// 显示股票详情
function showStockDetail(name, code, changePercent, industry, status) {
    const statusText = status === '成' ? '成功' : '失败';
    const statusColor = status === '成' ? '#28a745' : '#dc3545';

    const message = `
    <div style="text-align: left;">
        <h6 style="color: ${statusColor}; margin-bottom: 10px;">
            ${name} (${code})
        </h6>
        <p style="margin: 5px 0;"><strong>涨幅:</strong> ${changePercent}%</p>
        <p style="margin: 5px 0;"><strong>行业:</strong> ${industry}</p>
        <p style="margin: 5px 0;"><strong>状态:</strong> <span style="color: ${statusColor};">${statusText}</span></p>
    </div>
`;

    // 使用简单的alert，也可以替换为更美观的模态框
    if (typeof showToast !== 'undefined') {
        showToast(`${name}: ${changePercent}% (${statusText})`, status === '成' ? 'success' : 'danger');
    } else {
        alert(`${name} (${code})\n涨幅: ${changePercent}%\n行业: ${industry}\n状态: ${statusText}`);
    }
}

// 更新市场数据条
function updateMarketBars(data) {
    if (!data || data.length === 0) {
        document.getElementById('market-bars').innerHTML = '<div class="text-center text-muted">暂无数据</div>';
        document.getElementById('volume-info').innerHTML = '<div class="text-center text-muted">暂无数据</div>';
        return;
    }

    const latest = data[data.length - 1];
    if (!latest) {
        document.getElementById('market-bars').innerHTML = '<div class="text-center text-muted">数据格式错误</div>';
        return;
    }

    const riseCount = latest.rise_count || 0;
    const fallCount = latest.fall_count || 0;
    const flatCount = latest.flat_count || 0;
    const total = riseCount + fallCount + flatCount;

    if (total === 0) {
        document.getElementById('market-bars').innerHTML = '<div class="text-center text-muted">暂无交易数据</div>';
        return;
    }

    const risePercent = (riseCount / total * 100).toFixed(1);
    const fallPercent = (fallCount / total * 100).toFixed(1);
    const flatPercent = (flatCount / total * 100).toFixed(1);

    const marketBarsHtml = `
    <span class="bar-label-left rise">涨 ${riseCount}</span>
    <div class="bar-container-horizontal">
        <div class="bar-segment-horizontal rise" style="width: ${risePercent}%"></div>
        <div class="bar-segment-horizontal flat" style="width: ${flatPercent}%"></div>
        <div class="bar-segment-horizontal fall" style="width: ${fallPercent}%"></div>
    </div>
    <span class="bar-label-right fall">跌 ${fallCount}</span>
`;

    document.getElementById('market-bars').innerHTML = marketBarsHtml;

    // 更新成交金额
    const volume = latest.volume || 0;
    const volumeHtml = `
    <div class="volume-value">${latest.volume_formatted || formatAmount(volume)}</div>
    <div class="volume-label">成交金额</div>
`;
    document.getElementById('volume-info').innerHTML = volumeHtml;
}

// 更新涨跌停数据条
function updateLimitBars(data) {
    if (!data || data.length === 0) {
        document.getElementById('limit-bars').innerHTML = '<div class="text-center text-muted" style="font-size: 0.7rem;">暂无数据</div>';
        return;
    }

    const latest = data[data.length - 1];
    if (!latest) {
        document.getElementById('limit-bars').innerHTML = '<div class="text-center text-muted" style="font-size: 0.7rem;">数据格式错误</div>';
        return;
    }

    const limitUp = latest.limit_up || latest.limit_up_total || 0;
    const limitDown = latest.limit_down || latest.limit_down_total || 0;
    const total = limitUp + limitDown;

    let limitUpPercent = 50;
    let limitDownPercent = 50;

    if (total > 0) {
        limitUpPercent = (limitUp / total * 100);
        limitDownPercent = (limitDown / total * 100);
    }

    const limitBarsHtml = `
    <span class="bar-label-left rise">涨停 ${limitUp}</span>
    <div class="bar-container-horizontal">
        <div class="bar-segment-horizontal limit-up" style="width: ${limitUpPercent}%"></div>
        <div class="bar-segment-horizontal limit-down" style="width: ${limitDownPercent}%"></div>
    </div>
    <span class="bar-label-right fall">跌停 ${limitDown}</span>
`;

    document.getElementById('limit-bars').innerHTML = limitBarsHtml;
}

// 初始化实时图表
function initializeRealtimeCharts() {
    // 连板进度实时图表
    const lianbanRealtimeCtx = document.getElementById('lianbanRealtimeChart').getContext('2d');
    lianbanRealtimeChart = new Chart(lianbanRealtimeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 8 },
                        padding: 8
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 100,
                    ticks: {
                        font: { size: 8 },
                        stepSize: 20,
                        callback: function (value) { return value + '%'; }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 8 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });

    // 市场涨跌实时图表
    const marketRealtimeCtx = document.getElementById('marketRealtimeChart').getContext('2d');
    marketRealtimeChart = new Chart(marketRealtimeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 8 },
                        padding: 8
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 4000,
                    ticks: {
                        font: { size: 8 },
                        stepSize: 500
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 8 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });

    // 涨跌停实时图表
    const limitRealtimeCtx = document.getElementById('limitRealtimeChart').getContext('2d');
    limitRealtimeChart = new Chart(limitRealtimeCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 8 },
                        padding: 8
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 80,
                    ticks: {
                        font: { size: 8 },
                        stepSize: 10
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 8 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });

    // 成交金额实时图表
    const volumeRealtimeCtx = document.getElementById('volumeRealtimeChart').getContext('2d');
    volumeRealtimeChart = new Chart(volumeRealtimeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 8 },
                        padding: 8
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 15000,
                    ticks: {
                        font: { size: 8 },
                        stepSize: 2500,
                        callback: function (value) { return formatAmount(value); }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 8 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });
}

// 初始化合并图表
function initializeMergedCharts() {
    console.log('开始初始化合并图表');

    // 连板进度合并图表
    const lianbanMergedElement = document.getElementById('lianbanMergedChart');
    console.log('连板合并图表元素:', lianbanMergedElement);

    if (!lianbanMergedElement) {
        console.error('找不到连板合并图表元素');
        return;
    }

    const lianbanMergedCtx = lianbanMergedElement.getContext('2d');
    window.lianbanMergedChart = lianbanMergedChart = new Chart(lianbanMergedCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 10 },
                        padding: 10
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 100,
                    ticks: {
                        font: { size: 10 },
                        stepSize: 20,
                        callback: function (value) { return value + '%'; }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 9 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });

    console.log('连板合并图表创建成功:', lianbanMergedChart);

    // 市场涨跌合并图表
    const marketMergedCtx = document.getElementById('marketMergedChart').getContext('2d');
    window.marketMergedChart = marketMergedChart = new Chart(marketMergedCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 10 },
                        padding: 10
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 5000,
                    ticks: {
                        font: { size: 10 },
                        stepSize: 500
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 9 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });

    // 涨跌停合并图表
    const limitMergedCtx = document.getElementById('limitMergedChart').getContext('2d');
    window.limitMergedChart = limitMergedChart = new Chart(limitMergedCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 10 },
                        padding: 10
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 120,
                    ticks: {
                        font: { size: 10 },
                        stepSize: 20
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 9 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });

    // 成交金额合并图表
    const volumeMergedCtx = document.getElementById('volumeMergedChart').getContext('2d');
    window.volumeMergedChart = volumeMergedChart = new Chart(volumeMergedCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 10 },
                        padding: 10
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 25000,
                    ticks: {
                        font: { size: 10 },
                        stepSize: 5000,
                        callback: function (value) {
                            return formatAmount(value);
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 9 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });
}

// 验证连板数据是否有效
function isValidLianbanData(data) {
    if (!data || typeof data !== 'object') return false;

    // 检查是否有连板进度数据
    if (!data.lianban_progress || typeof data.lianban_progress !== 'object') return false;

    // 检查是否至少有一个级别的数据
    const levels = ['首板', '1进2', '2进3', '3进4', '4进5'];
    const hasValidLevel = levels.some(level =>
        data.lianban_progress[level] &&
        typeof data.lianban_progress[level] === 'string' &&
        data.lianban_progress[level].includes('/')
    );

    return hasValidLevel;
}

// 验证市场数据是否有效
function isValidMarketData(data) {
    if (!data || typeof data !== 'object') return false;

    // 检查必要的数字字段
    const requiredFields = ['up_count', 'down_count', 'volume', 'limit_up_count', 'limit_down_count'];
    return requiredFields.every(field =>
        typeof data[field] === 'number' &&
        !isNaN(data[field]) &&
        data[field] >= 0
    );
}

// 加载当天实时数据历史
async function loadRealtimeHistory() {
    try {
        // 加载连板实时数据历史
        const lianbanResponse = await fetch('/api/realtime/history/lianban');
        const lianbanResult = await lianbanResponse.json();
        if (lianbanResult.success && lianbanResult.data.length > 0) {
            realtimeDataCache.lianban = lianbanResult.data;
            updateRealtimeLianbanChart();
        }

        // 加载市场实时数据历史
        const marketResponse = await fetch('/api/realtime/history/market');
        const marketResult = await marketResponse.json();
        if (marketResult.success && marketResult.data.length > 0) {
            realtimeDataCache.market = marketResult.data;
            updateRealtimeMarketChart();
            updateRealtimeLimitChart();
            updateRealtimeVolumeChart();
        }

        console.log(`加载实时数据历史: 连板${realtimeDataCache.lianban.length}条, 市场${realtimeDataCache.market.length}条`);

        // 更新合并图表
        updateMergedCharts();
    } catch (error) {
        console.error('加载实时数据历史失败:', error);
    }
}

// 加载实时数据
async function loadRealtimeData() {
    try {
        await Promise.all([
            loadRealtimeLianbanData(),
            loadRealtimeMarketData()
        ]);
        updateRealtimeIndicators();
    } catch (error) {
        console.error('实时数据加载失败:', error);
    }
}

// 加载实时连板数据
async function loadRealtimeLianbanData() {
    try {
        const response = await fetch('/api/realtime/lianban_progress');
        const result = await response.json();

        if (result.success && isValidLianbanData(result.data)) {
            const currentTime = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            // 检查是否已存在相同时间的数据，避免重复
            const existingIndex = realtimeDataCache.lianban.findIndex(item => item.time === currentTime);
            if (existingIndex >= 0) {
                // 更新现有数据
                realtimeDataCache.lianban[existingIndex] = {
                    time: currentTime,
                    data: result.data
                };
            } else {
                // 添加新数据到缓存
                realtimeDataCache.lianban.push({
                    time: currentTime,
                    data: result.data
                });

                // 保持最近100个数据点
                if (realtimeDataCache.lianban.length > 100) {
                    realtimeDataCache.lianban.shift();
                }
            }

            updateRealtimeLianbanChart();
            updateLianbanMergedChart();
        } else if (result.success) {
            console.warn('连板数据无效，保持之前的数据:', result.data);
        }
    } catch (error) {
        console.error('实时连板数据加载失败:', error);
    }
}

// 加载实时市场数据
async function loadRealtimeMarketData() {
    try {
        const response = await fetch('/api/realtime/market_summary');
        const result = await response.json();

        if (result.success && isValidMarketData(result.data)) {
            const currentTime = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            // 检查是否已存在相同时间的数据，避免重复
            const existingIndex = realtimeDataCache.market.findIndex(item => item.time === currentTime);
            if (existingIndex >= 0) {
                // 更新现有数据
                realtimeDataCache.market[existingIndex] = {
                    time: currentTime,
                    data: result.data
                };
            } else {
                // 添加新数据到缓存
                realtimeDataCache.market.push({
                    time: currentTime,
                    data: result.data
                });

                // 保持最近100个数据点
                if (realtimeDataCache.market.length > 100) {
                    realtimeDataCache.market.shift();
                }
            }

            updateRealtimeMarketChart();
            updateRealtimeLimitChart();
            updateRealtimeVolumeChart();

            // 更新合并图表
            updateMarketMergedChart();
            updateLimitMergedChart();
            updateVolumeMergedChart();
        } else if (result.success) {
            console.warn('市场数据无效，保持之前的数据:', result.data);
        }
    } catch (error) {
        console.error('实时市场数据加载失败:', error);
    }
}

// 更新实时连板图表
function updateRealtimeLianbanChart() {
    if (realtimeDataCache.lianban.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const labels = realtimeDataCache.lianban.map(item => item.time);
    const levels = currentLianbanView === 'main' ? ['首板', '2进3', '3进4'] : ['3进4', '5进6', '9进10'];

    const datasets = levels.map((level, index) => {
        const data = realtimeDataCache.lianban.map(item => {
            const progress = item.data.lianban_progress;
            if (progress && progress[level]) {
                const match = progress[level].match(/(\d+)\/(\d+)=(\d+)%/);
                return match ? parseInt(match[3]) : 0;
            }
            return 0;
        });

        return {
            label: level,
            data: data,
            borderColor: getMainLevelColor(index),
            backgroundColor: getMainLevelColor(index) + '20',
            fill: false,
            tension: 0.1
        };
    });

    lianbanRealtimeChart.data.labels = labels;
    lianbanRealtimeChart.data.datasets = datasets;
    lianbanRealtimeChart.update();
}

// 更新实时市场图表
function updateRealtimeMarketChart() {
    if (realtimeDataCache.market.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const labels = realtimeDataCache.market.map(item => item.time);
    const upData = realtimeDataCache.market.map(item => item.data.up_count || 0);
    const downData = realtimeDataCache.market.map(item => item.data.down_count || 0);

    const datasets = [
        {
            label: '上涨',
            data: upData,
            borderColor: colors.success,
            backgroundColor: colors.success + '20',
            fill: false
        },
        {
            label: '下跌',
            data: downData,
            borderColor: colors.danger,
            backgroundColor: colors.danger + '20',
            fill: false
        }
    ];

    marketRealtimeChart.data.labels = labels;
    marketRealtimeChart.data.datasets = datasets;
    marketRealtimeChart.update();
}

// 更新实时涨跌停图表
function updateRealtimeLimitChart() {
    if (realtimeDataCache.market.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const labels = realtimeDataCache.market.map(item => item.time);
    const limitUpData = realtimeDataCache.market.map(item => item.data.limit_up_count || 0);
    const limitDownData = realtimeDataCache.market.map(item => item.data.limit_down_count || 0);

    const datasets = [
        {
            label: '涨停',
            data: limitUpData,
            backgroundColor: colors.danger,
            borderColor: colors.danger,
            borderWidth: 1
        },
        {
            label: '跌停',
            data: limitDownData,
            backgroundColor: colors.success,
            borderColor: colors.success,
            borderWidth: 1
        }
    ];

    limitRealtimeChart.data.labels = labels;
    limitRealtimeChart.data.datasets = datasets;
    limitRealtimeChart.update();
}

// 更新实时成交金额图表
function updateRealtimeVolumeChart() {
    if (realtimeDataCache.market.length === 0) return;

    const labels = realtimeDataCache.market.map(item => item.time);
    const volumeData = realtimeDataCache.market.map(item => {
        const volume = item.data.volume;
        if (typeof volume === 'string') {
            // 解析类似 "1.2万亿" 的格式
            if (volume.includes('万亿')) {
                return parseFloat(volume.replace('万亿', '')) * 10000;
            } else if (volume.includes('亿')) {
                return parseFloat(volume.replace('亿', ''));
            } else if (volume.includes('万')) {
                return parseFloat(volume.replace('万', '')) / 10000; // 转换为亿
            }
        }
        // 如果是数字，假设单位是亿
        return parseFloat(volume) || 0;
    });

    const datasets = [{
        label: '成交金额(亿)',
        data: volumeData,
        borderColor: colors.primary,
        backgroundColor: colors.primary + '20',
        fill: true,
        tension: 0.1
    }];

    volumeRealtimeChart.data.labels = labels;
    volumeRealtimeChart.data.datasets = datasets;
    volumeRealtimeChart.update();
}

// 更新实时指示器
function updateRealtimeIndicators() {
    const indicators = ['lianban', 'market', 'limit', 'volume'];
    indicators.forEach(type => {
        const indicator = document.getElementById(`${type}-realtime-indicator`);
        if (indicator) {
            indicator.style.color = '#28a745';
            setTimeout(() => {
                indicator.style.color = '#6c757d';
            }, 1000);
        }
    });
}

// 更新合并图表
function updateMergedCharts() {
    updateLianbanMergedChart();
    updateMarketMergedChart();
    updateLimitMergedChart();
    updateVolumeMergedChart();
}

// 更新连板进度合并图表
function updateLianbanMergedChart() {
    if (!lianbanMergedChart) {
        console.log('连板合并图表对象不存在');
        return;
    }

    console.log('开始更新连板合并图表');
    console.log('currentLianbanData长度:', currentLianbanData ? currentLianbanData.length : 0);
    console.log('realtimeDataCache.lianban.length:', realtimeDataCache.lianban.length);

    // 处理历史数据
    let historyLabels = [];
    let historyDatasets = [];

    if (currentLianbanData && currentLianbanData.length > 0) {
        // 历史数据标签（日期）
        historyLabels = currentLianbanData.map(item => formatDate(item.date));

        // 根据当前视图选择级别
        const levels = currentLianbanView === 'main' ? ['首板', '1进2', '2进3', '3进4'] : ['3进4', '5进6', '9进10'];
        const getColorFunc = currentLianbanView === 'main' ? getMainLevelColor : getHighLevelColor;

        historyDatasets = levels.map((level, index) => {
            const rawData = currentLianbanData.map(item => item.progress[level]?.percentage || 0);

            // 对100%的数据点进行处理，避免图表失真
            const processedData = rawData.map(value => {
                if (value === 100) {
                    return Math.random() * 5 + 95; // 95-100之间的随机值
                }
                return value;
            });

            return {
                label: level,
                data: processedData,
                borderColor: getColorFunc(index),
                backgroundColor: getColorFunc(index) + '20',
                fill: false,
                tension: 0.1
            };
        });

        console.log('历史数据处理完成，数据点数:', historyLabels.length);
    }

    // 处理实时数据
    let realtimeLabels = [];
    let realtimeDatasets = [];

    if (realtimeDataCache.lianban.length > 0) {
        realtimeLabels = realtimeDataCache.lianban.map(item => item.time);

        const levels = currentLianbanView === 'main' ? ['首板', '1进2', '2进3', '3进4'] : ['3进4', '5进6', '9进10'];
        const getColorFunc = currentLianbanView === 'main' ? getMainLevelColor : getHighLevelColor;

        realtimeDatasets = levels.map((level, index) => {
            const data = realtimeDataCache.lianban.map(item => {
                const progress = item.data.lianban_progress;
                if (progress && progress[level]) {
                    const match = progress[level].match(/(\d+)\/(\d+)=(\d+)%/);
                    return match ? parseInt(match[3]) : 0;
                }
                return 0;
            });

            return {
                label: level + '(实时)',
                data: data,
                borderColor: getColorFunc(index),
                backgroundColor: getColorFunc(index) + '40',
                fill: false,
                tension: 0.1,
                borderDash: [5, 5] // 虚线表示实时数据
            };
        });

        console.log('实时数据处理完成，数据点数:', realtimeLabels.length);
    }

    // 调整标签分配，历史数据占一半空间，实时数据占一半空间
    let mergedLabels = [];
    const mergedDatasets = [];

    // 计算空间分配 - 固定总数量，确保50%-50%分配
    const totalPoints = 60; // 总数据点数
    const historyPoints = 30; // 历史数据占30个点
    const realtimePoints = 30; // 实时数据占30个点

    // 为历史数据创建均匀分布的标签
    const historySpacedLabels = [];
    for (let i = 0; i < historyPoints; i++) {
        if (historyLabels.length > 0) {
            // 均匀采样历史标签
            const index = Math.floor((i / (historyPoints - 1)) * (historyLabels.length - 1));
            historySpacedLabels.push(historyLabels[Math.min(index, historyLabels.length - 1)]);
        } else {
            historySpacedLabels.push('');
        }
    }

    // 为实时数据创建均匀分布的标签
    const realtimeSpacedLabels = [];
    for (let i = 0; i < realtimePoints; i++) {
        if (realtimeLabels.length > 0) {
            // 均匀采样实时标签
            const index = Math.floor((i / (realtimePoints - 1)) * (realtimeLabels.length - 1));
            realtimeSpacedLabels.push(realtimeLabels[Math.min(index, realtimeLabels.length - 1)]);
        } else {
            realtimeSpacedLabels.push('');
        }
    }

    // 合并标签
    mergedLabels = [...historySpacedLabels, ...realtimeSpacedLabels];

    // 合并每个级别的历史和实时数据
    if (historyDatasets.length > 0) {
        const levels = currentLianbanView === 'main' ? ['首板', '1进2', '2进3', '3进4'] : ['3进4', '5进6', '9进10'];
        const getColorFunc = currentLianbanView === 'main' ? getMainLevelColor : getHighLevelColor;

        levels.forEach((level, index) => {
            const historyData = historyDatasets[index]?.data || [];
            const realtimeData = realtimeDatasets[index]?.data || [];

            // 调整历史数据到30个点，均匀采样
            const historySpacedData = [];
            for (let i = 0; i < historyPoints; i++) {
                if (historyData.length > 0) {
                    const index = Math.floor((i / (historyPoints - 1)) * (historyData.length - 1));
                    historySpacedData.push(historyData[Math.min(index, historyData.length - 1)]);
                } else {
                    historySpacedData.push(null);
                }
            }

            // 调整实时数据到30个点，均匀采样
            const realtimeSpacedData = [];
            for (let i = 0; i < realtimePoints; i++) {
                if (realtimeData.length > 0) {
                    const index = Math.floor((i / (realtimePoints - 1)) * (realtimeData.length - 1));
                    realtimeSpacedData.push(realtimeData[Math.min(index, realtimeData.length - 1)]);
                } else {
                    realtimeSpacedData.push(null);
                }
            }

            // 合并数据
            const mergedData = [...historySpacedData, ...realtimeSpacedData];

            mergedDatasets.push({
                label: level,
                data: mergedData,
                borderColor: getColorFunc(index),
                backgroundColor: getColorFunc(index) + '20',
                fill: false,
                tension: 0.1,
                spanGaps: true // 跨越null值
            });
        });
    }

    // 更新图表
    lianbanMergedChart.data.labels = mergedLabels;
    lianbanMergedChart.data.datasets = mergedDatasets;
    lianbanMergedChart.update();

    console.log('连板合并图表更新完成，总数据点数:', mergedLabels.length);
    console.log('图表数据集数量:', mergedDatasets.length);
    console.log('第一个数据集样本:', mergedDatasets[0] ? mergedDatasets[0].data.slice(0, 5) : '无数据');
}

// 更新市场涨跌合并图表
function updateMarketMergedChart() {
    if (!marketMergedChart) return;

    // 处理历史数据
    let historyLabels = [];
    let historyDatasets = [];

    if (currentMarketData && currentMarketData.length > 0) {
        historyLabels = currentMarketData.map(item => formatDate(item.date));

        historyDatasets = [
            {
                label: '上涨',
                data: currentMarketData.map(item => item.rise_count || 0),
                borderColor: colors.success,
                backgroundColor: colors.success + '20',
                fill: false,
                tension: 0.1
            },
            {
                label: '下跌',
                data: currentMarketData.map(item => item.fall_count || 0),
                borderColor: colors.danger,
                backgroundColor: colors.danger + '20',
                fill: false,
                tension: 0.1
            }
        ];
    }

    // 处理实时数据
    let realtimeLabels = [];
    let realtimeDatasets = [];

    if (realtimeDataCache.market.length > 0) {
        realtimeLabels = realtimeDataCache.market.map(item => item.time);

        realtimeDatasets = [
            {
                label: '上涨(实时)',
                data: realtimeDataCache.market.map(item => item.data.rise_count || 0),
                borderColor: colors.success,
                backgroundColor: colors.success + '40',
                fill: false,
                tension: 0.1
            },
            {
                label: '下跌(实时)',
                data: realtimeDataCache.market.map(item => item.data.fall_count || 0),
                borderColor: colors.danger,
                backgroundColor: colors.danger + '40',
                fill: false,
                tension: 0.1
            }
        ];
    }

    // 调整空间分配（50%-50%）
    const historyPoints = 30;
    const realtimePoints = 30;

    // 创建合并标签 - 均匀采样
    const historySpacedLabels = [];
    for (let i = 0; i < historyPoints; i++) {
        if (historyLabels.length > 0) {
            const index = Math.floor((i / (historyPoints - 1)) * (historyLabels.length - 1));
            historySpacedLabels.push(historyLabels[Math.min(index, historyLabels.length - 1)]);
        } else {
            historySpacedLabels.push('');
        }
    }

    const realtimeSpacedLabels = [];
    for (let i = 0; i < realtimePoints; i++) {
        if (realtimeLabels.length > 0) {
            const index = Math.floor((i / (realtimePoints - 1)) * (realtimeLabels.length - 1));
            realtimeSpacedLabels.push(realtimeLabels[Math.min(index, realtimeLabels.length - 1)]);
        } else {
            realtimeSpacedLabels.push('');
        }
    }

    const mergedLabels = [...historySpacedLabels, ...realtimeSpacedLabels];

    // 创建合并数据集
    const mergedDatasets = [];

    ['上涨', '下跌'].forEach((type, index) => {
        const historyData = historyDatasets[index]?.data || [];
        const realtimeData = realtimeDatasets[index]?.data || [];

        // 调整数据到指定空间 - 均匀采样
        const historySpacedData = [];
        for (let i = 0; i < historyPoints; i++) {
            if (historyData.length > 0) {
                const index = Math.floor((i / (historyPoints - 1)) * (historyData.length - 1));
                historySpacedData.push(historyData[Math.min(index, historyData.length - 1)]);
            } else {
                historySpacedData.push(null);
            }
        }

        const realtimeSpacedData = [];
        for (let i = 0; i < realtimePoints; i++) {
            if (realtimeData.length > 0) {
                const index = Math.floor((i / (realtimePoints - 1)) * (realtimeData.length - 1));
                realtimeSpacedData.push(realtimeData[Math.min(index, realtimeData.length - 1)]);
            } else {
                realtimeSpacedData.push(null);
            }
        }

        const mergedData = [...historySpacedData, ...realtimeSpacedData];

        mergedDatasets.push({
            label: type,
            data: mergedData,
            borderColor: index === 0 ? colors.success : colors.danger,
            backgroundColor: (index === 0 ? colors.success : colors.danger) + '20',
            fill: false,
            tension: 0.1,
            spanGaps: true
        });
    });

    marketMergedChart.data.labels = mergedLabels;
    marketMergedChart.data.datasets = mergedDatasets;
    marketMergedChart.update();
}

// 更新涨跌停合并图表
function updateLimitMergedChart() {
    if (!limitMergedChart) return;

    // 处理历史数据
    let historyLabels = [];
    let historyDatasets = [];

    if (currentLimitData && currentLimitData.length > 0) {
        historyLabels = currentLimitData.map(item => formatDate(item.date));

        historyDatasets = [
            {
                label: '涨停',
                data: currentLimitData.map(item => item.limit_up || 0),
                backgroundColor: colors.success,
                borderColor: colors.success,
                fill: false
            },
            {
                label: '跌停',
                data: currentLimitData.map(item => item.limit_down || 0),
                backgroundColor: colors.danger,
                borderColor: colors.danger,
                fill: false
            }
        ];
    }

    // 处理实时数据
    let realtimeLabels = [];
    let realtimeDatasets = [];

    if (realtimeDataCache.market.length > 0) {
        realtimeLabels = realtimeDataCache.market.map(item => item.time);

        realtimeDatasets = [
            {
                label: '涨停(实时)',
                data: realtimeDataCache.market.map(item => item.data.limit_up || 0),
                backgroundColor: colors.success + '80',
                borderColor: colors.success,
                fill: false
            },
            {
                label: '跌停(实时)',
                data: realtimeDataCache.market.map(item => item.data.limit_down || 0),
                backgroundColor: colors.danger + '80',
                borderColor: colors.danger,
                fill: false
            }
        ];
    }

    // 调整空间分配（50%-50%）
    const historyPoints = 30;
    const realtimePoints = 30;

    // 创建合并标签 - 均匀采样
    const historySpacedLabels = [];
    for (let i = 0; i < historyPoints; i++) {
        if (historyLabels.length > 0) {
            const index = Math.floor((i / (historyPoints - 1)) * (historyLabels.length - 1));
            historySpacedLabels.push(historyLabels[Math.min(index, historyLabels.length - 1)]);
        } else {
            historySpacedLabels.push('');
        }
    }

    const realtimeSpacedLabels = [];
    for (let i = 0; i < realtimePoints; i++) {
        if (realtimeLabels.length > 0) {
            const index = Math.floor((i / (realtimePoints - 1)) * (realtimeLabels.length - 1));
            realtimeSpacedLabels.push(realtimeLabels[Math.min(index, realtimeLabels.length - 1)]);
        } else {
            realtimeSpacedLabels.push('');
        }
    }

    const mergedLabels = [...historySpacedLabels, ...realtimeSpacedLabels];

    // 创建合并数据集
    const mergedDatasets = [];

    ['涨停', '跌停'].forEach((type, index) => {
        const historyData = historyDatasets[index]?.data || [];
        const realtimeData = realtimeDatasets[index]?.data || [];

        // 调整数据到指定空间 - 均匀采样
        const historySpacedData = [];
        for (let i = 0; i < historyPoints; i++) {
            if (historyData.length > 0) {
                const index = Math.floor((i / (historyPoints - 1)) * (historyData.length - 1));
                historySpacedData.push(historyData[Math.min(index, historyData.length - 1)]);
            } else {
                historySpacedData.push(null);
            }
        }

        const realtimeSpacedData = [];
        for (let i = 0; i < realtimePoints; i++) {
            if (realtimeData.length > 0) {
                const index = Math.floor((i / (realtimePoints - 1)) * (realtimeData.length - 1));
                realtimeSpacedData.push(realtimeData[Math.min(index, realtimeData.length - 1)]);
            } else {
                realtimeSpacedData.push(null);
            }
        }

        const mergedData = [...historySpacedData, ...realtimeSpacedData];

        mergedDatasets.push({
            label: type,
            data: mergedData,
            backgroundColor: index === 0 ? colors.success : colors.danger,
            borderColor: index === 0 ? colors.success : colors.danger,
            spanGaps: true
        });
    });

    limitMergedChart.data.labels = mergedLabels;
    limitMergedChart.data.datasets = mergedDatasets;
    limitMergedChart.update();
}

// 更新成交金额合并图表
function updateVolumeMergedChart() {
    if (!volumeMergedChart) return;

    // 处理历史数据
    let historyLabels = [];
    let historyDatasets = [];

    if (currentVolumeData && currentVolumeData.length > 0) {
        historyLabels = currentVolumeData.map(item => formatDate(item.date));

        historyDatasets = [{
            label: '成交金额',
            data: currentVolumeData.map(item => {
                // 将volume从分转换为亿（除以100000000）
                return (item.volume || 0) / 100000000;
            }),
            borderColor: colors.primary,
            backgroundColor: colors.primary + '20',
            fill: true,
            tension: 0.1
        }];
    }

    // 处理实时数据
    let realtimeLabels = [];
    let realtimeDatasets = [];

    if (realtimeDataCache.market.length > 0) {
        realtimeLabels = realtimeDataCache.market.map(item => item.time);

        realtimeDatasets = [{
            label: '成交金额(实时)',
            data: realtimeDataCache.market.map(item => {
                const volume = item.data.volume;
                if (typeof volume === 'string') {
                    // 解析类似 "1.2万亿" 的格式
                    if (volume.includes('万亿')) {
                        return parseFloat(volume.replace('万亿', '')) * 10000;
                    } else if (volume.includes('亿')) {
                        return parseFloat(volume.replace('亿', ''));
                    } else if (volume.includes('万')) {
                        return parseFloat(volume.replace('万', '')) / 10000; // 转换为亿
                    }
                }
                // 如果是数字，假设单位是亿
                return parseFloat(volume) || 0;
            }),
            borderColor: colors.primary,
            backgroundColor: colors.primary + '40',
            fill: true,
            tension: 0.1
        }];
    }

    // 调整空间分配（50%-50%）
    const historyPoints = 30;
    const realtimePoints = 30;

    // 创建合并标签 - 均匀采样
    const historySpacedLabels = [];
    for (let i = 0; i < historyPoints; i++) {
        if (historyLabels.length > 0) {
            const index = Math.floor((i / (historyPoints - 1)) * (historyLabels.length - 1));
            historySpacedLabels.push(historyLabels[Math.min(index, historyLabels.length - 1)]);
        } else {
            historySpacedLabels.push('');
        }
    }

    const realtimeSpacedLabels = [];
    for (let i = 0; i < realtimePoints; i++) {
        if (realtimeLabels.length > 0) {
            const index = Math.floor((i / (realtimePoints - 1)) * (realtimeLabels.length - 1));
            realtimeSpacedLabels.push(realtimeLabels[Math.min(index, realtimeLabels.length - 1)]);
        } else {
            realtimeSpacedLabels.push('');
        }
    }

    const mergedLabels = [...historySpacedLabels, ...realtimeSpacedLabels];

    // 创建合并数据集
    const historyData = historyDatasets[0]?.data || [];
    const realtimeData = realtimeDatasets[0]?.data || [];

    // 调整数据到指定空间 - 均匀采样
    const historySpacedData = [];
    for (let i = 0; i < historyPoints; i++) {
        if (historyData.length > 0) {
            const index = Math.floor((i / (historyPoints - 1)) * (historyData.length - 1));
            historySpacedData.push(historyData[Math.min(index, historyData.length - 1)]);
        } else {
            historySpacedData.push(null);
        }
    }

    const realtimeSpacedData = [];
    for (let i = 0; i < realtimePoints; i++) {
        if (realtimeData.length > 0) {
            const index = Math.floor((i / (realtimePoints - 1)) * (realtimeData.length - 1));
            realtimeSpacedData.push(realtimeData[Math.min(index, realtimeData.length - 1)]);
        } else {
            realtimeSpacedData.push(null);
        }
    }

    const mergedData = [...historySpacedData, ...realtimeSpacedData];

    const mergedDatasets = [{
        label: '成交金额(亿)',
        data: mergedData,
        borderColor: colors.primary,
        backgroundColor: colors.primary + '20',
        fill: true,
        tension: 0.1,
        spanGaps: true
    }];

    volumeMergedChart.data.labels = mergedLabels;
    volumeMergedChart.data.datasets = mergedDatasets;
    volumeMergedChart.update();
}
